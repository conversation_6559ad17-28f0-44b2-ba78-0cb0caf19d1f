import { BackendAuthContext } from '@tech-notes/shared';

import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import {
  VehicleBrandV3Service,
  CreateBrandV3Data,
  UpdateBrandV3Data,
} from './vehicle-brand-v3.service.js';
import {
  VehicleModelV3Service,
  CreateModelV3Data,
  UpdateModelV3Data,
} from './vehicle-model-v3.service.js';
import {
  VehicleModelYearV3Service,
  CreateModelYearV3Data,
} from './vehicle-model-year-v3.service.js';
import {
  VehicleSubBrandV3Service,
  CreateSubBrandV3Data,
  UpdateSubBrandV3Data,
} from './vehicle-sub-brand-v3.service.js';
import {
  VehicleYearV3Service,
  CreateYearV3Data,
  UpdateYearV3Data,
} from './vehicle-year-v3.service.js';

/**
 * Coordinator service for Vehicle Hierarchy V3 operations
 * Handles cross-entity operations and complex queries that span multiple entities
 * Maintains backward compatibility with the original monolithic service interface
 */
export class VehicleHierarchyV3CoordinatorService {
  constructor(
    private brandService: VehicleBrandV3Service,
    private subBrandService: VehicleSubBrandV3Service,
    private modelService: VehicleModelV3Service,
    private yearService: VehicleYearV3Service,
    private modelYearService: VehicleModelYearV3Service,
    private prismaService: PrismaService,
    private logger: Logger
  ) {}

  // ===== BRAND OPERATIONS (Delegate to Brand Service) =====

  async getBrandsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.getBrandsByTenant(tenantId, BackendAuthContext);
  }

  async getBrandById(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.getBrandById(
      brandId,
      tenantId,
      BackendAuthContext
    );
  }

  async createBrand(
    brandData: CreateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.createBrand(
      brandData,
      tenantId,
      BackendAuthContext
    );
  }

  async updateBrand(
    brandId: string,
    brandData: UpdateBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.updateBrand(
      brandId,
      brandData,
      tenantId,
      BackendAuthContext
    );
  }

  async deleteBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.deleteBrand(brandId, tenantId, BackendAuthContext);
  }

  async reorderBrands(
    brandOrders: Array<{ id: string; displayOrder: number }>,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.brandService.reorderBrands(
      brandOrders,
      tenantId,
      BackendAuthContext
    );
  }

  // ===== SUB-BRAND OPERATIONS (Delegate to Sub-Brand Service) =====

  async getSubBrandsByBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.getSubBrandsByBrand(
      brandId,
      tenantId,
      BackendAuthContext
    );
  }

  async getSubBrandById(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.getSubBrandById(
      subBrandId,
      tenantId,
      BackendAuthContext
    );
  }

  async createSubBrand(
    subBrandData: CreateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.createSubBrand(
      subBrandData,
      tenantId,
      BackendAuthContext
    );
  }

  async updateSubBrand(
    subBrandId: string,
    updateData: UpdateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.updateSubBrand(
      subBrandId,
      updateData,
      tenantId,
      BackendAuthContext
    );
  }

  async deleteSubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.deleteSubBrand(
      subBrandId,
      tenantId,
      BackendAuthContext
    );
  }

  async reorderSubBrands(
    brandId: string,
    subBrandOrders: Array<{ id: string; displayOrder: number }>,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.subBrandService.reorderSubBrands(
      brandId,
      subBrandOrders,
      tenantId,
      BackendAuthContext
    );
  }

  // ===== MODEL OPERATIONS (Delegate to Model Service) =====

  async getModelsBySubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.getModelsBySubBrand(
      subBrandId,
      tenantId,
      BackendAuthContext
    );
  }

  async getModelById(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.getModelById(
      modelId,
      tenantId,
      BackendAuthContext
    );
  }

  async createModel(
    modelData: CreateModelV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.createModel(
      modelData,
      tenantId,
      BackendAuthContext
    );
  }

  async updateModel(
    modelId: string,
    updateData: UpdateModelV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.updateModel(
      modelId,
      updateData,
      tenantId,
      BackendAuthContext
    );
  }

  async deleteModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.deleteModel(modelId, tenantId, BackendAuthContext);
  }

  async reorderModels(
    subBrandId: string,
    modelOrders: Array<{ id: string; displayOrder: number }>,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelService.reorderModels(
      subBrandId,
      modelOrders,
      tenantId,
      BackendAuthContext
    );
  }

  // ===== VALIDATION HELPERS =====

  async validateBrandOwnership(brandId: string, tenantId: string) {
    return this.brandService.validateBrandOwnership(brandId, tenantId);
  }

  async validateSubBrandOwnership(subBrandId: string, tenantId: string) {
    return this.subBrandService.validateSubBrandOwnership(subBrandId, tenantId);
  }

  async validateModelOwnership(modelId: string, tenantId: string) {
    return this.modelService.validateModelOwnership(modelId, tenantId);
  }

  // ===== YEAR OPERATIONS (Delegate to Year Service) =====

  async getYearsByTenant(
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.getYearsByTenant(tenantId, BackendAuthContext);
  }

  async getYearById(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.getYearById(yearId, tenantId, BackendAuthContext);
  }

  async createYear(
    yearData: CreateYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.createYear(yearData, tenantId, BackendAuthContext);
  }

  async updateYear(
    yearId: string,
    yearData: UpdateYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.updateYear(
      yearId,
      yearData,
      tenantId,
      BackendAuthContext
    );
  }

  async deleteYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.deleteYear(yearId, tenantId, BackendAuthContext);
  }

  async reorderYears(
    yearOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.yearService.reorderYears(
      yearOrders,
      tenantId,
      BackendAuthContext
    );
  }

  // ===== MODEL-YEAR ASSOCIATION OPERATIONS =====

  async getYearsByModel(
    modelId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelYearService.getYearsByModel(
      modelId,
      tenantId,
      BackendAuthContext
    );
  }

  async getModelsByYear(
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelYearService.getModelsByYear(
      yearId,
      tenantId,
      BackendAuthContext
    );
  }

  async createModelYear(
    associationData: CreateModelYearV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelYearService.createModelYear(
      associationData,
      tenantId,
      BackendAuthContext
    );
  }

  async deleteModelYear(
    modelId: string,
    yearId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ) {
    return this.modelYearService.deleteModelYear(
      modelId,
      yearId,
      tenantId,
      BackendAuthContext
    );
  }

  // ===== FUTURE: COMPLEX CROSS-ENTITY OPERATIONS =====

  /**
   * Get full hierarchy tree for a tenant (future implementation)
   * This would return Brand → Sub-Brand → Model structure
   */
  async getFullHierarchy() {
    // TODO: Implement full hierarchy query
    // This would join across all three entities to build the complete tree
    throw new Error('Full hierarchy query not yet implemented');
  }

  /**
   * Bulk operations across entities (future implementation)
   */
  async bulkCreateHierarchy() {
    // TODO: Implement bulk creation across Brand → Sub-Brand → Model
    throw new Error('Bulk hierarchy creation not yet implemented');
  }
}
