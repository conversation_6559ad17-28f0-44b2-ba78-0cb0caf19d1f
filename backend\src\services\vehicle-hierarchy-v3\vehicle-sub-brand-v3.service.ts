import { VehicleSubBrandV3 } from '@prisma/client';
import { BackendAuthContext } from '@tech-notes/shared';

import { NotFoundError } from '../../types/error.types.js';
import { Logger } from '../../utils/logger.js';
import { PrismaService } from '../prisma.service.js';

import { BaseVehicleHierarchyV3Service } from './base-vehicle-hierarchy-v3.service.js';
import { VehicleBrandV3Service } from './vehicle-brand-v3.service.js';

export interface CreateSubBrandV3Data {
  name: string;
  brandId: string;
}

export interface UpdateSubBrandV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

/**
 * Service for managing Vehicle Sub-Brand V3 operations
 * Handles CRUD operations for sub-brands with brand relationship validation
 */
export class VehicleSubBrandV3Service extends BaseVehicleHierarchyV3Service {
  constructor(
    prismaService: PrismaService,
    logger: Logger,
    private brandService: VehicleBrandV3Service
  ) {
    super(prismaService, logger);
  }

  /**
   * Get all sub-brands for a specific brand
   */
  async getSubBrandsByBrand(
    brandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3[]> {
    this.logOperation('getSubBrandsByBrand', { brandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        // Validate brand ownership first
        await this.brandService.validateBrandOwnership(brandId, tenantId);

        return await this.prisma.vehicleSubBrandV3.findMany({
          where: this.getActiveEntityWhere({
            brandId,
            tenantId,
          }),
          orderBy: { displayOrder: 'asc' },
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Get a specific sub-brand by ID
   */
  async getSubBrandById(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3 | null> {
    this.logOperation('getSubBrandById', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        return await this.prisma.vehicleSubBrandV3.findFirst({
          where: this.getActiveEntityWhere({
            id: subBrandId,
            tenantId,
          }),
        });
      },
      BackendAuthContext
    );
  }

  /**
   * Create a new sub-brand
   */
  async createSubBrand(
    subBrandData: CreateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3> {
    this.logOperation('createSubBrand', {
      name: subBrandData.name,
      brandId: subBrandData.brandId,
      tenantId,
    });

    const trimmedName = this.validateAndTrimName(
      subBrandData.name,
      'Sub-brand'
    );

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate brand ownership first
          await this.brandService.validateBrandOwnership(
            subBrandData.brandId,
            tenantId
          );

          // Get next display order for this brand
          const nextDisplayOrder = await this.getNextDisplayOrder(
            this.prisma.vehicleSubBrandV3,
            {
              brandId: subBrandData.brandId,
              tenantId,
            }
          );

          return await this.prisma.vehicleSubBrandV3.create({
            data: {
              name: trimmedName,
              brandId: subBrandData.brandId,
              tenantId,
              displayOrder: nextDisplayOrder,
            },
          });
        } catch (error) {
          this.handleUniqueConstraintError(error, trimmedName, 'Sub-brand');
          this.logError('createSubBrand', error as Error, {
            subBrandData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Update a sub-brand
   */
  async updateSubBrand(
    subBrandId: string,
    updateData: UpdateSubBrandV3Data,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<VehicleSubBrandV3> {
    this.logOperation('updateSubBrand', { subBrandId, updateData, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Prepare update data
          const dataToUpdate: Record<string, unknown> = {};

          if (updateData.name !== undefined) {
            dataToUpdate.name = this.validateAndTrimName(
              updateData.name,
              'Sub-brand'
            );
          }

          if (updateData.isActive !== undefined) {
            dataToUpdate.isActive = updateData.isActive;
          }

          if (updateData.displayOrder !== undefined) {
            dataToUpdate.displayOrder = updateData.displayOrder;
          }

          return await this.prisma.vehicleSubBrandV3.update({
            where: {
              id: subBrandId,
              tenantId,
              deletedAt: null,
            },
            data: dataToUpdate,
          });
        } catch (error) {
          this.handleNotFoundError(error, subBrandId, 'Sub-brand');
          this.handleUniqueConstraintError(
            error,
            updateData.name || '',
            'Sub-brand'
          );
          this.logError('updateSubBrand', error as Error, {
            subBrandId,
            updateData,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Soft delete a sub-brand
   */
  async deleteSubBrand(
    subBrandId: string,
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('deleteSubBrand', { subBrandId, tenantId });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          await this.executeSoftDelete(
            this.prisma.vehicleSubBrandV3,
            subBrandId,
            { tenantId }
          );
        } catch (error) {
          this.handleNotFoundError(error, subBrandId, 'Sub-brand');
          this.logError('deleteSubBrand', error as Error, {
            subBrandId,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Reorder sub-brands within a brand
   */
  async reorderSubBrands(
    brandId: string,
    subBrandOrders: { id: string; displayOrder: number }[],
    tenantId: string,
    BackendAuthContext?: BackendAuthContext
  ): Promise<void> {
    this.logOperation('reorderSubBrands', {
      brandId,
      subBrandOrders,
      tenantId,
    });

    return await this.withTenantScopeForUser(
      tenantId,
      async () => {
        try {
          // Validate brand ownership first
          await this.brandService.validateBrandOwnership(brandId, tenantId);

          await this.executeReorder('vehicleSubBrandV3', subBrandOrders, {
            brandId,
            tenantId,
          });
        } catch (error) {
          this.logError('reorderSubBrands', error as Error, {
            brandId,
            subBrandOrders,
            tenantId,
          });
          throw error;
        }
      },
      BackendAuthContext
    );
  }

  /**
   * Validate that a sub-brand exists and belongs to the tenant
   * Used by other services for relationship validation
   */
  async validateSubBrandOwnership(
    subBrandId: string,
    tenantId: string
  ): Promise<void> {
    const subBrand = await this.prisma.vehicleSubBrandV3.findFirst({
      where: this.getActiveEntityWhere({
        id: subBrandId,
        tenantId,
      }),
      select: { id: true },
    });

    if (!subBrand) {
      throw new NotFoundError(
        `Sub-brand ${subBrandId} not found or not accessible`
      );
    }
  }
}
