/**
 * Model Management Modal Component
 * Follows the established patterns from SubBrandManagementModal but handles cascading Brand → Sub-Brand selection
 */

import React from "react";
import {
  <PERSON>dal,
  <PERSON><PERSON>,
  Input,
  Badge,
  <PERSON>ert,
  <PERSON><PERSON><PERSON><PERSON>,
  FormField,
  CompanyAdminOnly,
} from "../index";
import { Plus, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Car } from "lucide-react";
import { useTypedApi } from "../../services/api-client";
import { getModelConfig } from "./configs/entity-configs";
import { useModelCRUD } from "../../hooks/useModelCRUD";

interface ModelManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ModelManagementModal: React.FC<ModelManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const api = useTypedApi();
  const config = getModelConfig(api);

  // Use the Model specific CRUD hook
  const crud = useModelCRUD(config, isOpen);

  // Color scheme configuration (matching orange theme)
  const colorClasses = {
    container: 'bg-orange-50 border-orange-200',
    text: 'text-orange-900',
    header: 'text-orange-900',
  };

  // Handle close - combine external onClose with crud cleanup
  const handleClose = () => {
    crud.handleClose();
    onClose();
  };

  // Loading state (matching SimpleEntityModal pattern)
  if (crud.isLoading) {
    return (
      <Modal isOpen={isOpen} onClose={handleClose} title={config.ui.title} size="xl">
        <div className="flex items-center justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={config.ui.title} size="xl">
      <div className="space-y-6">
        {/* Add New Model Form - Always Visible (matching SimpleEntityModal) */}
        <CompanyAdminOnly>
          <div className={`p-4 rounded-lg ${colorClasses.container}`}>
            <h3 className={`text-lg font-medium mb-3 ${colorClasses.header}`}>
              Add New {config.entityName}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField label="Sub-Brand" required>
                <select
                  value={crud.selectedSubBrandId}
                  onChange={(e) => crud.setSelectedSubBrandId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent h-[46px]"
                  disabled={crud.isPending}
                >
                  <option value="">Select a sub-brand...</option>
                  {crud.subBrands.map((subBrand) => (
                    <option key={subBrand.id} value={subBrand.id}>
                      {subBrand.name} ({subBrand.brand?.name || "Unknown Brand"})
                    </option>
                  ))}
                </select>
              </FormField>

              <FormField label={`${config.entityName} Name`} required>
                <Input
                  type="text"
                  placeholder={config.ui.placeholder}
                  value={crud.newModelName}
                  onChange={(e) => crud.setNewModelName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !crud.isPending && crud.newModelName.trim() && crud.selectedSubBrandId) {
                      crud.handleCreate();
                    }
                  }}
                  maxLength={500}
                  disabled={crud.isPending}
                />
              </FormField>

              <div className="flex items-end">
                <Button
                  onClick={crud.handleCreate}
                  disabled={crud.isPending || !crud.newModelName.trim() || !crud.selectedSubBrandId}
                  className="flex items-center space-x-2 h-[46px] px-4 w-full"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add {config.entityName}</span>
                </Button>
              </div>
            </div>
          </div>
        </CompanyAdminOnly>

        {/* Edit Model Form (matching SimpleEntityModal pattern) */}
        {crud.editingModel && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-medium text-yellow-900 mb-3">
              Edit {config.entityName}: {crud.editingModel.name}
            </h3>
            <div className="space-y-4">
              <FormField label="Sub-Brand" required>
                <select
                  value={crud.editSelectedSubBrandId}
                  onChange={(e) => crud.setEditSelectedSubBrandId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  disabled={crud.isPending}
                >
                  <option value="">Select a sub-brand...</option>
                  {crud.subBrands.map((subBrand) => (
                    <option key={subBrand.id} value={subBrand.id}>
                      {subBrand.name} ({subBrand.brand?.name || "Unknown Brand"})
                    </option>
                  ))}
                </select>
              </FormField>

              <FormField label={`${config.entityName} Name`} required>
                <Input
                  type="text"
                  value={crud.editModelName}
                  onChange={(e) => crud.setEditModelName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !crud.isPending && crud.editModelName.trim() && crud.editSelectedSubBrandId) {
                      crud.handleUpdate();
                    }
                    if (e.key === "Escape") {
                      crud.setEditingModel(null);
                    }
                  }}
                  maxLength={500}
                  disabled={crud.isPending}
                />
              </FormField>

              <FormField label="Status">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="modelActive"
                      checked={crud.editModelActive}
                      onChange={() => crud.setEditModelActive(true)}
                      disabled={crud.isPending}
                      className="mr-2"
                    />
                    Active
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="modelActive"
                      checked={!crud.editModelActive}
                      onChange={() => crud.setEditModelActive(false)}
                      disabled={crud.isPending}
                      className="mr-2"
                    />
                    Inactive
                  </label>
                </div>
              </FormField>

              <div className="flex items-center space-x-3">
                <Button
                  onClick={crud.handleUpdate}
                  disabled={crud.isPending || !crud.editModelName.trim() || !crud.editSelectedSubBrandId}
                  variant="primary"
                >
                  Save Changes
                </Button>
                <Button
                  onClick={() => crud.setEditingModel(null)}
                  disabled={crud.isPending}
                  variant="outline"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Models List (matching SimpleEntityModal pattern) */}
        <div className="border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <h3 className="text-lg font-medium text-gray-900">
              {config.ui.title.replace('Manage ', '')} ({crud.sortedModels.length})
            </h3>
          </div>

          {crud.sortedModels.length === 0 ? (
            <div className="p-8 text-center">
              <Car className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No {config.entityName.toLowerCase()}s yet
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                {config.ui.emptyStateMessage}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {crud.sortedModels.map((model) => (
                <div
                  key={model.id}
                  className="px-4 py-4 flex items-center justify-between hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <Car className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {model.name}
                      </span>
                      <span className="text-sm text-gray-500">
                        ({model.subBrand?.brand?.name || "Unknown Brand"} - {model.subBrand?.name || "Unknown Sub-Brand"})
                      </span>
                      <Badge
                        variant={model.isActive ? "success" : "secondary"}
                        size="sm"
                      >
                        {model.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>

                  <CompanyAdminOnly>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => crud.handleEdit(model)}
                        disabled={crud.isPending}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => crud.handleDelete(model)}
                        disabled={crud.isPending}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CompanyAdminOnly>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Delete confirmation (matching SimpleEntityModal pattern) */}
        {crud.deleteConfirmModel && (
          <Alert variant="warning" className="border-orange-200 bg-orange-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900">
                  Confirm Deletion
                </h3>
                <p className="text-orange-800 mt-1">
                  Are you sure you want to delete {config.entityName.toLowerCase()} "{crud.deleteConfirmModel.name}"
                  from {crud.deleteConfirmModel.subBrand?.brand?.name || "Unknown Brand"} - {crud.deleteConfirmModel.subBrand?.name || "Unknown Sub-Brand"}?
                  This action cannot be undone and may affect associated data.
                </p>
                <div className="flex items-center space-x-3 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => crud.setDeleteConfirmModel(null)}
                    disabled={crud.isPending}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={crud.confirmDelete}
                    disabled={crud.isPending}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    {crud.currentOperation === 'delete' ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                    ) : (
                      `Delete ${config.entityName}`
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </Alert>
        )}
      </div>
    </Modal>
  );
};
