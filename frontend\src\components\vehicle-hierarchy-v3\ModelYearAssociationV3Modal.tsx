/**
 * Model-Year Association V3 Modal Component
 * Allows managing year associations for a specific model with checkboxes
 */

import React, { useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { Modal, Button, Alert, Badge, LoadingSpinner, CompanyAdminOnly } from "../index";
import { useTypedApi, type VehicleModelV3 } from "../../services/api-client";
import { Calendar, Check } from "lucide-react";

interface ModelYearAssociationV3ModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: VehicleModelV3 | null;
}

export const ModelYearAssociationV3Modal: React.FC<ModelYearAssociationV3ModalProps> = ({
  isOpen,
  onClose,
  model,
}) => {
  const api = useTypedApi();
  const queryClient = useQueryClient();

  const [selectedYearIds, setSelectedYearIds] = useState<string[]>([]);
  const [currentYearIds, setCurrentYearIds] = useState<string[]>([]);

  // Fetch all available years
  const { data: yearsResponse, isLoading: isLoadingYears } = useQuery({
    queryKey: ["vehicle-years-v3"],
    queryFn: () => api.vehicleHierarchyV3.getYears(),
    enabled: isOpen,
  });

  // Fetch current model-year associations
  const { data: modelYearsResponse, isLoading: isLoadingModelYears } = useQuery({
    queryKey: ["vehicle-model-years-v3", model?.id],
    queryFn: () => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV3.getYearsByModel(model.id);
    },
    enabled: isOpen && !!model,
  });

  // Update selected years when data loads
  useEffect(() => {
    if (modelYearsResponse?.data) {
      const yearIds = modelYearsResponse.data.map(year => year.id);
      setSelectedYearIds(yearIds);
      setCurrentYearIds(yearIds);
    }
  }, [modelYearsResponse]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedYearIds([]);
      setCurrentYearIds([]);
    }
  }, [isOpen]);

  // Create model-year association mutation
  const createAssociationMutation = useMutation({
    mutationFn: (yearId: string) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV3.createModelYear(model.id, {
        modelId: model.id,
        yearId,
      });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create association");
    },
  });

  // Delete model-year association mutation
  const deleteAssociationMutation = useMutation({
    mutationFn: (yearId: string) => {
      if (!model) throw new Error("No model selected");
      return api.vehicleHierarchyV3.deleteModelYear(model.id, yearId);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to remove association");
    },
  });

  const handleYearToggle = (yearId: string) => {
    setSelectedYearIds(prev => 
      prev.includes(yearId) 
        ? prev.filter(id => id !== yearId)
        : [...prev, yearId]
    );
  };

  const handleSaveChanges = async () => {
    if (!model) return;

    const yearsToAdd = selectedYearIds.filter(id => !currentYearIds.includes(id));
    const yearsToRemove = currentYearIds.filter(id => !selectedYearIds.includes(id));

    try {
      // Remove associations
      for (const yearId of yearsToRemove) {
        await deleteAssociationMutation.mutateAsync(yearId);
      }

      // Add new associations
      for (const yearId of yearsToAdd) {
        await createAssociationMutation.mutateAsync(yearId);
      }

      toast.success("Year associations updated successfully");
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["vehicle-model-years-v3", model.id] });
      queryClient.invalidateQueries({ queryKey: ["vehicle-models-v3"] });
      
      handleClose();
    } catch {
      // Error handling is done in the mutations
    }
  };

  const handleClose = () => {
    setSelectedYearIds([]);
    setCurrentYearIds([]);
    onClose();
  };

  const years = yearsResponse?.data || [];
  const isLoading = isLoadingYears || isLoadingModelYears;
  const isPending = createAssociationMutation.isPending || deleteAssociationMutation.isPending;
  const hasChanges = JSON.stringify(selectedYearIds.sort()) !== JSON.stringify(currentYearIds.sort());

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Manage Model Years" size="lg">
      <div className="space-y-6">
        {/* Model Info */}
        {model && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium text-blue-900">
                  Managing Years for: {model.name}
                </h3>
                <p className="text-sm text-blue-700">
                  Select which years this model is available for
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Error State */}
        {!isLoading && years.length === 0 && (
          <Alert variant="warning">
            No years available. Please create some years first.
          </Alert>
        )}

        {/* Years Selection */}
        {!isLoading && years.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Available Years</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {years.map((year) => {
                const isSelected = selectedYearIds.includes(year.id);
                const wasOriginallySelected = currentYearIds.includes(year.id);
                
                return (
                  <label
                    key={year.id}
                    className={`
                      relative flex items-center justify-center p-3 border rounded-lg cursor-pointer transition-all duration-200
                      ${isSelected 
                        ? 'bg-blue-50 border-blue-300 text-blue-900' 
                        : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                      }
                      ${!year.isActive ? 'opacity-50' : ''}
                    `}
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleYearToggle(year.id)}
                      disabled={!year.isActive || isPending}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-2">
                      <div className={`
                        w-4 h-4 border rounded flex items-center justify-center
                        ${isSelected 
                          ? 'bg-blue-600 border-blue-600' 
                          : 'border-gray-300'
                        }
                      `}>
                        {isSelected && <Check className="w-3 h-3 text-white" />}
                      </div>
                      <span className="font-medium">{year.name}</span>
                    </div>
                    
                    {/* Change indicator */}
                    {isSelected !== wasOriginallySelected && (
                      <div className={`
                        absolute -top-1 -right-1 w-3 h-3 rounded-full
                        ${isSelected ? 'bg-green-500' : 'bg-red-500'}
                      `} />
                    )}
                  </label>
                );
              })}
            </div>
          </div>
        )}

        {/* Actions */}
        <CompanyAdminOnly>
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-gray-600">
              {selectedYearIds.length} year(s) selected
              {hasChanges && (
                <Badge variant="secondary" className="ml-2">
                  Changes pending
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveChanges}
                disabled={!hasChanges || isPending}
                className="flex items-center space-x-2"
              >
                {isPending && <LoadingSpinner size="sm" />}
                <span>Save Changes</span>
              </Button>
            </div>
          </div>
        </CompanyAdminOnly>
      </div>
    </Modal>
  );
};
