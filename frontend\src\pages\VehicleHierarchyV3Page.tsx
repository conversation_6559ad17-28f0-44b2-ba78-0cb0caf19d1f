import { useState } from "react";
import { useAuth } from "@clerk/clerk-react";
import { Navigate } from "react-router-dom";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON>ge,
  Loading<PERSON>pinner,
  <PERSON><PERSON>,
} from "../components";
import { YearManagementV3Modal } from "../components/vehicle-hierarchy-v3/YearManagementV3Modal";
import { BrandManagementV3Modal } from "../components/vehicle-hierarchy-v3/BrandManagementV3Modal";
import { SubBrandManagementV3Modal } from "../components/vehicle-hierarchy-v3/SubBrandManagementV3Modal";
import { ModelManagementV3Modal } from "../components/vehicle-hierarchy-v3/ModelManagementV3Modal";
import { usePermissions } from "../hooks/usePermissions";
import {
  Calendar,
  Building2,
  Car,
  Lock,
  TreePine
} from "lucide-react";

export function VehicleHierarchyV3Page() {
  const { isLoaded, isSignedIn } = useAuth();
  const { isCompanyAdmin, isCompanyTech } = usePermissions();

  // Modal states
  const [isYearModalOpen, setIsYearModalOpen] = useState(false);
  const [isBrandModalOpen, setIsBrandModalOpen] = useState(false);
  const [isSubBrandModalOpen, setIsSubBrandModalOpen] = useState(false);
  const [isModelModalOpen, setIsModelModalOpen] = useState(false);

  // Show loading while auth is being determined
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  // Redirect to sign-in if not authenticated
  if (!isSignedIn) {
    return <Navigate to="/sign-in" replace />;
  }

  // Check permissions - require at least Company Tech access
  if (!isCompanyAdmin && !isCompanyTech) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Alert variant="error">
          <div className="flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>You don't have permission to access this page.</span>
          </div>
        </Alert>
      </div>
    );
  }

  const managementButtons = [
    {
      id: "year",
      title: "Years",
      icon: Calendar,
      color: "blue",
      enabled: true,
      onClick: () => setIsYearModalOpen(true),
    },
    {
      id: "brand",
      title: "Brands",
      icon: Building2,
      color: "blue",
      enabled: true,
      onClick: () => setIsBrandModalOpen(true),
    },
    {
      id: "subBrand",
      title: "Sub-Brands",
      icon: TreePine,
      color: "blue",
      enabled: true,
      onClick: () => setIsSubBrandModalOpen(true),
    },
    {
      id: "model",
      title: "Models",
      icon: Car,
      color: "blue",
      enabled: true,
      onClick: () => setIsModelModalOpen(true),
    },
  ];

  const getButtonColorClasses = (color: string, enabled: boolean) => {
    if (!enabled) {
      return "bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed";
    }
    
    const colorMap = {
      blue: "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100",
      green: "bg-green-50 border-green-200 text-green-700 hover:bg-green-100",
      purple: "bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100",
      orange: "bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100",
      red: "bg-red-50 border-red-200 text-red-700 hover:bg-red-100",
    };
    
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getIconColorClasses = (color: string, enabled: boolean) => {
    if (!enabled) {
      return "text-gray-400";
    }
    
    const colorMap = {
      blue: "text-blue-600",
      green: "text-green-600",
      purple: "text-purple-600",
      orange: "text-orange-600",
      red: "text-red-600",
    };
    
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Vehicle Hierarchy Management (V3)
        </h1>
        <p className="mt-2 text-gray-600">
          Manage your vehicle hierarchy with the new 4-tier system: Brand → Sub-Brand → Model ↔ Year
        </p>
      </div>



      {/* Management Buttons Section */}
      <Card>
        <CardHeader
          title="Hierarchy Management"
          subtitle="Choose a category to manage your vehicle hierarchy data"
        />
        <CardContent>
          <div className="flex flex-wrap md:flex-nowrap gap-3">
            {managementButtons.map((button) => {
              const IconComponent = button.icon;
              return (
                <div key={button.id} className="relative flex-1 md:flex-none md:min-w-0">
                  <button
                    onClick={button.enabled ? button.onClick : undefined}
                    disabled={!button.enabled}
                    className={`w-full p-3 border rounded-lg transition-all duration-200 hover:shadow-md ${getButtonColorClasses(
                      button.color,
                      button.enabled
                    )}`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`p-1.5 rounded-lg bg-white shadow-sm ${
                        button.enabled ? "shadow-md" : "shadow-sm"
                      }`}>
                        <IconComponent
                          className={`h-4 w-4 ${getIconColorClasses(button.color, button.enabled)}`}
                        />
                      </div>
                      <div className="flex-1 text-left">
                        <h3 className="font-medium text-sm">
                          {button.title}
                        </h3>
                        {!button.enabled && (
                          <Badge variant="secondary" size="sm" className="mt-0.5">
                            Coming Soon
                          </Badge>
                        )}
                      </div>
                    </div>
                  </button>

                  {!button.enabled && (
                    <div className="absolute inset-0 bg-gray-100 bg-opacity-50 rounded-lg flex items-center justify-center">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Hierarchy Tree Section - Placeholder for future phases */}
      <Card>
        <CardHeader
          title="Vehicle Hierarchy Tree"
          subtitle="Complete hierarchy view with expand/collapse controls (Coming in future phases)"
        />
        <CardContent>
          <div className="text-center py-12">
            <TreePine className="mx-auto h-16 w-16 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">
              Hierarchy Tree View Coming Soon
            </h3>
            <p className="mt-2 text-gray-600 max-w-md mx-auto">
              The accordion-style tree view will be available in future phases, 
              allowing you to explore the complete vehicle hierarchy with expand/collapse controls.
            </p>
            <div className="mt-6">
              <Badge variant="secondary" size="md">
                Phase 2+ Feature
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <YearManagementV3Modal
        isOpen={isYearModalOpen}
        onClose={() => setIsYearModalOpen(false)}
      />
      <BrandManagementV3Modal
        isOpen={isBrandModalOpen}
        onClose={() => setIsBrandModalOpen(false)}
      />
      <SubBrandManagementV3Modal
        isOpen={isSubBrandModalOpen}
        onClose={() => setIsSubBrandModalOpen(false)}
      />
      <ModelManagementV3Modal
        isOpen={isModelModalOpen}
        onClose={() => setIsModelModalOpen(false)}
      />
    </div>
  );
}
