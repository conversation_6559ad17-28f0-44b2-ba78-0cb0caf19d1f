import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';

import { MiddlewareFactory } from '../../../middleware/middleware-factory.js';
import {
  createValidationMiddleware,
  commonSchemas,
} from '../../../middleware/validation.middleware.js';
import { VehicleHierarchyV3CoordinatorService } from '../../../services/vehicle-hierarchy-v3/vehicle-hierarchy-v3-coordinator.service.js';
import { Logger } from '../../../utils/logger.js';
import { getRequestUser } from '../../../utils/request-types.js';

interface ServiceDependencies {
  vehicleHierarchyV3Service: VehicleHierarchyV3CoordinatorService;
  middlewareFactory: MiddlewareFactory;
  logger: Logger;
}

// Validation schemas
const createBrandV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Brand name is required')
    .max(500, 'Brand name too long')
    .trim(),
});

const updateBrandV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Brand name is required')
    .max(500, 'Brand name too long')
    .trim()
    .optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().int().min(1).optional(),
});

const reorderBrandsV3Schema = z.object({
  brandOrders: z.array(
    z.object({
      id: z.string().cuid('Invalid brand ID format'),
      displayOrder: z.number().int().min(1),
    })
  ),
});

const createSubBrandV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Sub-brand name is required')
    .max(500, 'Sub-brand name too long')
    .trim(),
});

const updateSubBrandV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Sub-brand name is required')
    .max(500, 'Sub-brand name too long')
    .trim()
    .optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().int().min(1).optional(),
});

const reorderSubBrandsV3Schema = z.object({
  subBrandOrders: z.array(
    z.object({
      id: z.string().cuid('Invalid sub-brand ID format'),
      displayOrder: z.number().int().min(1),
    })
  ),
});

const idParamSchema = commonSchemas.idParam;
const brandIdParamSchema = z.object({
  brandId: z.string().cuid('Invalid brand ID format'),
});
const subBrandIdParamSchema = z.object({
  subBrandId: z.string().cuid('Invalid sub-brand ID format'),
});
const modelIdParamSchema = z.object({
  modelId: z.string().cuid('Invalid model ID format'),
});
const yearIdParamSchema = z.object({
  yearId: z.string().cuid('Invalid year ID format'),
});

const createModelV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Model name is required')
    .max(500, 'Model name too long')
    .trim(),
});

const updateModelV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Model name is required')
    .max(500, 'Model name too long')
    .trim()
    .optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().int().min(1).optional(),
});

const reorderModelsV3Schema = z.object({
  modelOrders: z.array(
    z.object({
      id: z.string().cuid('Invalid model ID format'),
      displayOrder: z.number().int().min(1),
    })
  ),
});

const createYearV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Year name is required')
    .max(500, 'Year name too long')
    .trim(),
});

const updateYearV3Schema = z.object({
  name: z
    .string()
    .min(1, 'Year name is required')
    .max(500, 'Year name too long')
    .trim()
    .optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().int().min(1).optional(),
});

const reorderYearsV3Schema = z.object({
  yearOrders: z.array(
    z.object({
      id: z.string().cuid('Invalid year ID format'),
      displayOrder: z.number().int().min(1),
    })
  ),
});

const createModelYearV3Schema = z.object({
  modelId: z.string().cuid('Invalid model ID format'),
  yearId: z.string().cuid('Invalid year ID format'),
});

/**
 * Factory function to create vehicle hierarchy V3 routes
 * Phase 1: Brand operations - COMPLETE
 * Phase 2: Sub-Brand operations - COMPLETE
 * Phase 3: Model operations - COMPLETE
 * Phase 4: Year operations - IN PROGRESS
 */
export function createVehicleHierarchyV3Router(
  dependencies: ServiceDependencies
): Router {
  const { vehicleHierarchyV3Service, middlewareFactory, logger } = dependencies;
  const router = Router();

  const validate = createValidationMiddleware({ logger });

  // ===== BRAND ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands
   * Get all brands for tenant
   */
  router.get(
    '/brands',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const brands = await vehicleHierarchyV3Service.getBrandsByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: brands,
          meta: {
            tenantId,
            count: brands.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch brands', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands/:id
   * Get brand by ID
   */
  router.get(
    '/brands/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        const brand = await vehicleHierarchyV3Service.getBrandById(
          id,
          tenantId,
          getRequestUser(req)!
        );

        if (!brand) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Brand not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: brand,
        });
      } catch (error) {
        logger.error('Failed to fetch brand', {
          error,
          brandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands
   * Create a new brand
   */
  router.post(
    '/brands',
    validate({ body: createBrandV3Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const brandData = req.body;

        const brand = await vehicleHierarchyV3Service.createBrand(
          brandData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: brand,
          message: 'Brand created successfully',
        });
      } catch (error) {
        logger.error('Failed to create brand', {
          error,
          brandData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v3/brands/:id
   * Update a brand
   */
  router.put(
    '/brands/:id',
    validate({ params: idParamSchema, body: updateBrandV3Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;
        const updateData = req.body;

        const brand = await vehicleHierarchyV3Service.updateBrand(
          id,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: brand,
          message: 'Brand updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update brand', {
          error,
          brandId: req.params.id,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v3/brands/:id
   * Delete a brand (soft delete)
   */
  router.delete(
    '/brands/:id',
    validate({ params: idParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { id } = req.params;

        await vehicleHierarchyV3Service.deleteBrand(
          id,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Brand deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete brand', {
          error,
          brandId: req.params.id,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands/reorder
   * Reorder brands within tenant
   */
  router.post(
    '/brands/reorder',
    validate({ body: reorderBrandsV3Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { brandOrders } = req.body;

        await vehicleHierarchyV3Service.reorderBrands(
          brandOrders,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Brands reordered successfully',
        });
      } catch (error) {
        logger.error('Failed to reorder brands', {
          error,
          brandOrders: req.body.brandOrders,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== SUB-BRAND ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands
   * Get all sub-brands for a specific brand
   */
  router.get(
    '/brands/:brandId/sub-brands',
    validate({ params: brandIdParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { brandId } = req.params;

        const subBrands = await vehicleHierarchyV3Service.getSubBrandsByBrand(
          brandId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: subBrands,
          message: 'Sub-brands retrieved successfully',
        });
      } catch (error) {
        logger.error('Failed to get sub-brands', {
          error,
          brandId: req.params.brandId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands
   * Create a new sub-brand
   */
  router.post(
    '/brands/:brandId/sub-brands',
    validate({
      params: brandIdParamSchema,
      body: createSubBrandV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { brandId } = req.params;
        const subBrandData = { ...req.body, brandId };

        const subBrand = await vehicleHierarchyV3Service.createSubBrand(
          subBrandData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: subBrand,
          message: 'Sub-brand created successfully',
        });
      } catch (error) {
        logger.error('Failed to create sub-brand', {
          error,
          subBrandData: req.body,
          brandId: req.params.brandId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId
   * Get a specific sub-brand by ID
   */
  router.get(
    '/brands/:brandId/sub-brands/:subBrandId',
    validate({
      params: brandIdParamSchema.merge(subBrandIdParamSchema),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;

        const subBrand = await vehicleHierarchyV3Service.getSubBrandById(
          subBrandId,
          tenantId,
          getRequestUser(req)!
        );

        if (!subBrand) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Sub-brand not found',
          });
        }

        res.json({
          data: subBrand,
          message: 'Sub-brand retrieved successfully',
        });
      } catch (error) {
        logger.error('Failed to get sub-brand', {
          error,
          subBrandId: req.params.subBrandId,
          brandId: req.params.brandId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId
   * Update a sub-brand
   */
  router.put(
    '/brands/:brandId/sub-brands/:subBrandId',
    validate({
      params: brandIdParamSchema.merge(subBrandIdParamSchema),
      body: updateSubBrandV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;
        const updateData = req.body;

        const subBrand = await vehicleHierarchyV3Service.updateSubBrand(
          subBrandId,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: subBrand,
          message: 'Sub-brand updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update sub-brand', {
          error,
          subBrandId: req.params.subBrandId,
          brandId: req.params.brandId,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId
   * Soft delete a sub-brand
   */
  router.delete(
    '/brands/:brandId/sub-brands/:subBrandId',
    validate({
      params: brandIdParamSchema.merge(subBrandIdParamSchema),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;

        await vehicleHierarchyV3Service.deleteSubBrand(
          subBrandId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Sub-brand deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete sub-brand', {
          error,
          subBrandId: req.params.subBrandId,
          brandId: req.params.brandId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/reorder
   * Reorder sub-brands within a brand
   */
  router.post(
    '/brands/:brandId/sub-brands/reorder',
    validate({
      params: brandIdParamSchema,
      body: reorderSubBrandsV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { brandId } = req.params;
        const { subBrandOrders } = req.body;

        await vehicleHierarchyV3Service.reorderSubBrands(
          brandId,
          subBrandOrders,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Sub-brands reordered successfully',
        });
      } catch (error) {
        logger.error('Failed to reorder sub-brands', {
          error,
          brandId: req.params.brandId,
          subBrandOrders: req.body.subBrandOrders,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MODEL ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models
   * Get all models for a specific sub-brand
   */
  router.get(
    '/brands/:brandId/sub-brands/:subBrandId/models',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
      }),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;

        const models = await vehicleHierarchyV3Service.getModelsBySubBrand(
          subBrandId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: models,
          message: 'Models retrieved successfully',
        });
      } catch (error) {
        logger.error('Failed to get models', {
          error,
          subBrandId: req.params.subBrandId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models
   * Create a new model
   */
  router.post(
    '/brands/:brandId/sub-brands/:subBrandId/models',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
      }),
      body: createModelV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;
        const { name } = req.body;

        const model = await vehicleHierarchyV3Service.createModel(
          { name, subBrandId },
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: model,
          message: 'Model created successfully',
        });
      } catch (error) {
        logger.error('Failed to create model', {
          error,
          subBrandId: req.params.subBrandId,
          modelData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models/:modelId
   * Get a specific model by ID
   */
  router.get(
    '/brands/:brandId/sub-brands/:subBrandId/models/:modelId',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
        modelId: z.string().cuid('Invalid model ID format'),
      }),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId } = req.params;

        const model = await vehicleHierarchyV3Service.getModelById(
          modelId,
          tenantId,
          getRequestUser(req)!
        );

        if (!model) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Model not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: model,
          message: 'Model retrieved successfully',
        });
      } catch (error) {
        logger.error('Failed to get model by ID', {
          error,
          modelId: req.params.modelId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models/:modelId
   * Update a model
   */
  router.put(
    '/brands/:brandId/sub-brands/:subBrandId/models/:modelId',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
        modelId: z.string().cuid('Invalid model ID format'),
      }),
      body: updateModelV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId } = req.params;
        const updateData = req.body;

        const model = await vehicleHierarchyV3Service.updateModel(
          modelId,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: model,
          message: 'Model updated successfully',
        });
      } catch (error) {
        logger.error('Failed to update model', {
          error,
          modelId: req.params.modelId,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models/:modelId
   * Soft delete a model
   */
  router.delete(
    '/brands/:brandId/sub-brands/:subBrandId/models/:modelId',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
        modelId: z.string().cuid('Invalid model ID format'),
      }),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId } = req.params;

        await vehicleHierarchyV3Service.deleteModel(
          modelId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model deleted successfully',
        });
      } catch (error) {
        logger.error('Failed to delete model', {
          error,
          modelId: req.params.modelId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/brands/:brandId/sub-brands/:subBrandId/models/reorder
   * Reorder models within a sub-brand
   */
  router.post(
    '/brands/:brandId/sub-brands/:subBrandId/models/reorder',
    validate({
      params: z.object({
        brandId: z.string().cuid('Invalid brand ID format'),
        subBrandId: z.string().cuid('Invalid sub-brand ID format'),
      }),
      body: reorderModelsV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { subBrandId } = req.params;
        const { modelOrders } = req.body;

        await vehicleHierarchyV3Service.reorderModels(
          subBrandId,
          modelOrders,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Models reordered successfully',
        });
      } catch (error) {
        logger.error('Failed to reorder models', {
          error,
          subBrandId: req.params.subBrandId,
          modelOrders: req.body.modelOrders,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== YEAR ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v3/years
   * Get all years for tenant
   */
  router.get(
    '/years',
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;

        const years = await vehicleHierarchyV3Service.getYearsByTenant(
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: years,
          meta: {
            tenantId,
            count: years.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch years', {
          error,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * GET /api/v1/vehicle-hierarchy-v3/years/:yearId
   * Get a specific year by ID
   */
  router.get(
    '/years/:yearId',
    validate({ params: yearIdParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { yearId } = req.params;

        const year = await vehicleHierarchyV3Service.getYearById(
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        if (!year) {
          return res.status(404).json({
            error: 'Not Found',
            message: 'Year not found',
            statusCode: 404,
            timestamp: new Date().toISOString(),
          });
        }

        res.json({
          data: year,
          meta: {
            tenantId,
            yearId,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch year', {
          error,
          yearId: req.params.yearId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/years
   * Create a new year
   */
  router.post(
    '/years',
    validate({ body: createYearV3Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const yearData = req.body;

        const year = await vehicleHierarchyV3Service.createYear(
          yearData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: year,
          meta: {
            tenantId,
            created: true,
          },
        });
      } catch (error) {
        logger.error('Failed to create year', {
          error,
          yearData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * PUT /api/v1/vehicle-hierarchy-v3/years/:yearId
   * Update a year
   */
  router.put(
    '/years/:yearId',
    validate({
      params: yearIdParamSchema,
      body: updateYearV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { yearId } = req.params;
        const updateData = req.body;

        const year = await vehicleHierarchyV3Service.updateYear(
          yearId,
          updateData,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: year,
          meta: {
            tenantId,
            yearId,
            updated: true,
          },
        });
      } catch (error) {
        logger.error('Failed to update year', {
          error,
          yearId: req.params.yearId,
          updateData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v3/years/:yearId
   * Soft delete a year
   */
  router.delete(
    '/years/:yearId',
    validate({ params: yearIdParamSchema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { yearId } = req.params;

        await vehicleHierarchyV3Service.deleteYear(
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Year deleted successfully',
          meta: {
            tenantId,
            yearId,
            deleted: true,
          },
        });
      } catch (error) {
        logger.error('Failed to delete year', {
          error,
          yearId: req.params.yearId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/years/reorder
   * Reorder years within tenant
   */
  router.post(
    '/years/reorder',
    validate({ body: reorderYearsV3Schema }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { yearOrders } = req.body;

        await vehicleHierarchyV3Service.reorderYears(
          yearOrders,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Years reordered successfully',
        });
      } catch (error) {
        logger.error('Failed to reorder years', {
          error,
          yearOrders: req.body.yearOrders,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  // ===== MODEL-YEAR ASSOCIATION ROUTES =====

  /**
   * GET /api/v1/vehicle-hierarchy-v3/models/:modelId/years
   * Get all years associated with a specific model
   */
  router.get(
    '/models/:modelId/years',
    validate({ params: modelIdParamSchema }),
    ...middlewareFactory.createAuthWithCompanyUser(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId } = req.params;

        const modelYears = await vehicleHierarchyV3Service.getYearsByModel(
          modelId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          data: modelYears,
          meta: {
            tenantId,
            modelId,
            count: modelYears.length,
          },
        });
      } catch (error) {
        logger.error('Failed to fetch years for model', {
          error,
          modelId: req.params.modelId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * POST /api/v1/vehicle-hierarchy-v3/models/:modelId/years
   * Create a new model-year association
   */
  router.post(
    '/models/:modelId/years',
    validate({
      params: modelIdParamSchema,
      body: createModelYearV3Schema,
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId } = req.params;
        const associationData = {
          ...req.body,
          modelId, // Ensure modelId from URL takes precedence
        };

        const modelYear = await vehicleHierarchyV3Service.createModelYear(
          associationData,
          tenantId,
          getRequestUser(req)!
        );

        res.status(201).json({
          data: modelYear,
          meta: {
            tenantId,
            modelId,
            created: true,
          },
        });
      } catch (error) {
        logger.error('Failed to create model-year association', {
          error,
          modelId: req.params.modelId,
          associationData: req.body,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  /**
   * DELETE /api/v1/vehicle-hierarchy-v3/models/:modelId/years/:yearId
   * Delete a model-year association
   */
  router.delete(
    '/models/:modelId/years/:yearId',
    validate({
      params: modelIdParamSchema.merge(yearIdParamSchema),
    }),
    ...middlewareFactory.createAuthWithCompanyAdmin(),
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = getRequestUser(req)!;
        const { modelId, yearId } = req.params;

        await vehicleHierarchyV3Service.deleteModelYear(
          modelId,
          yearId,
          tenantId,
          getRequestUser(req)!
        );

        res.json({
          message: 'Model-year association deleted successfully',
          meta: {
            tenantId,
            modelId,
            yearId,
            deleted: true,
          },
        });
      } catch (error) {
        logger.error('Failed to delete model-year association', {
          error,
          modelId: req.params.modelId,
          yearId: req.params.yearId,
          tenantId: getRequestUser(req)?.tenantId,
        });
        next(error);
      }
    }
  );

  return router;
}
